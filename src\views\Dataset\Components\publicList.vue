<script setup lang="ts">
  import { CustomForm } from '@/components';
  import type { IFormItem, IPage } from '@/interface';
  import { TABLE_PAGINATION } from '@/json/common';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table';
  import { ref, reactive, onMounted } from 'vue';
  import { convertIsoTimeToLocalTime } from '@/utils/common';
  import { fetchPublicDatasetList } from '@/api/dataset';
  import { useRouter } from 'vue-router';
  import type { IDatasetItems } from '@/interface/dateset';
  import { datasetTypes, dataSourceOptions, formatBytes } from '.';
  const router = useRouter();
  const loading = ref(false);
  const DEFAULT_SEARCHSTATE = {
    name: undefined,
    type: undefined,
    source: undefined,
  };
  const searchState = reactive({ ...DEFAULT_SEARCHSTATE });
  const tableHeight = ref(0);
  const formConfig: IFormItem[] = [
    {
      field: 'name',
      type: 'input',
      label: '数据集名称',
      placeholder: '请输入',
    },
    {
      field: 'type',
      type: 'select',
      label: '数据集类型',
      options: datasetTypes,
      placeholder: '请输入',
    },
    {
      field: 'source',
      type: 'select',
      label: '数据来源',
      options: dataSourceOptions,
      placeholder: '请输入',
    },
  ];
  const columns: ColumnType[] = [
    { title: '数据集名称', dataIndex: 'name', fixed: 'left' },
    { title: '简介', dataIndex: 'summary', width: 180, ellipsis: true },
    { title: '数据集类型', dataIndex: 'type' },
    { title: '数据集来源', dataIndex: 'source' },
    { title: '数据量', dataIndex: 'data_quantity', width: 100 },
    { title: '数据大小', dataIndex: 'file_size', width: 100 },
    { title: '创建时间', dataIndex: 'created_at' },
    { title: '发布时间', dataIndex: 'published_at' },
    { title: '操作', dataIndex: 'operation', fixed: 'right', width: 140 },
  ];
  const pagination = reactive({ ...TABLE_PAGINATION });
  const pageParame: IPage = reactive({ page: 1, limit: 10 });
  const dataSource = reactive<IDatasetItems[]>([]);

  const getList = async () => {
    loading.value = true;
    try {
      const { page, total_count, items } = (await fetchPublicDatasetList({
        ...pageParame,
        ...searchState,
      })) as IPage & {
        total_count: number;
        items: IDatasetItems[];
      };
      dataSource.length = 0;
      dataSource.push(...items);
      Object.assign(pagination, { current: page, total: total_count });
      loading.value = false;
    } catch {
      loading.value = false;
    }
  };
  const getTableHeight = () => {
    const tableItem = document.querySelector('.container');
    tableHeight.value = tableItem?.clientHeight as number;
  };
  const onFinish = (values: Record<string, string>) => {
    const searchConfig: { [key: string]: string } = {};
    for (let key in values) {
      searchConfig[key] = values[key];
    }
    Object.assign(searchState, { ...searchConfig });
    pageParame.page = 1;
    getList();
  };
  const onRest = () => {
    Object.assign(pagination, { ...TABLE_PAGINATION });
    Object.assign(pageParame, { page: 1, limit: 10 });
    Object.assign(searchState, { ...DEFAULT_SEARCHSTATE });
    getList();
  };

  const toggleTable = (_pagination: TablePaginationConfig) => {
    let { current, pageSize } = _pagination;
    console.log(current, pageSize);
    Object.assign(pagination, { current, pageSize });
    Object.assign(pageParame, { page: current, limit: pageSize });
    getList();
  };
  const serverInfo = (id: string) => {
    router.push(`/dataset/detail/${id}`);
  };
  onMounted(() => {
    getTableHeight();
    getList();
  });
</script>

<template>
  <CustomForm style="margin-bottom: 0" :form-items="formConfig" @on-finish="onFinish" @on-rest="onRest" />
  <a-table
    :data-source="dataSource"
    :columns="columns"
    :pagination="pagination"
    :loading="loading"
    :scroll="{ y: tableHeight - 230, x: 'max-content' }"
    @change="toggleTable"
  >
    <template #bodyCell="{ column, record, text }">
      <div v-if="column.dataIndex === 'operation'" class="operation-box">
        <a @click="serverInfo(record.id)">查看</a>
      </div>
      <div v-else-if="['created_at', 'published_at'].includes(column.dataIndex)">
        {{ convertIsoTimeToLocalTime(text) }}
      </div>
      <div v-else-if="column.dataIndex === 'name'">
        <div>{{ record.name }}</div>
        <div class="text-#797979">{{ record.id }}</div>
      </div>
      <div v-else-if="column.dataIndex === 'file_size'">
        {{ formatBytes(text) }}
      </div>
      <div v-else-if="column.dataIndex === 'type'">
        {{ datasetTypes.find((item) => item.value === text)?.label }}
      </div>
      <div v-else-if="column.dataIndex === 'source'">
        {{ dataSourceOptions.find((item) => item.value === text)?.label }}
      </div>
      <div v-else>{{ text || '--' }}</div>
    </template>
  </a-table>
</template>

<style scoped lang="less">
  :deep(.ant-table-cell-ellipsis div) {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
