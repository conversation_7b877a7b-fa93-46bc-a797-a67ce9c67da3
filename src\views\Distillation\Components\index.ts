export const DistillationStatus = [
  { label: '创建中', value: 'creating', color: 'orange' },
  { label: '运行中', value: 'running', color: 'orange' },
  { label: '创建失败', value: 'create_failed', color: 'red' },
  { label: '停止中', value: 'stopping', color: 'orange' },
  { label: '已停止', value: 'stopped', color: 'green' },
  { label: '已完成', value: 'completed', color: 'green' },
  { label: '训练失败', value: 'training_failed', color: 'red' },
];

export const STATUS_MAP = {
  CREATING: 'creating', // 创建中
  RUNNING: 'running', // 运行中
  CREATE_FAILED: 'create_failed', // 创建失败
  STOPPING: 'stopping', // 停止中
  STOPPED: 'stopped', // 已停止
  COMPLETED: 'completed', // 已完成
  TRAINING_FAILED: 'training_failed', // 训练失败
}

export const distillation_parameters = [
  {
    label: 'lmbda',
    value: 'lmbda',
    desc: '控制学生数据分数的 Lambda 参数（符合策略的学生生成的输出的比例）。0时为Supervised KD，1时为GKD。可在[0,1]范围内选择，这样就会混合比例',
    type: 'inputNumber',
    min: 0,
    max: 1,
    step: 0.1,
    precision: 1,
    required: true,
  },
  {
    label: 'beta',
    value: 'beta',
    desc: '控制Jensen-Shannon Divergence的插值。beta=0时损失函数近似于为正向KL散度， beta=1时为近似于为反向KL散度。beta值在[0,1]范围内时，在两者之间进行插值。',
    type: 'inputNumber',
    min: 0,
    max: 1,
    step: 0.1,
    precision: 1,
    required: true,
  },
  {
    label: 'per-device-train-batch-size',
    value: 'per-device-train-batch-size',
    desc: '批处理大小',
    type: 'inputNumber',
    min: 0,
    required: true,
  },
  {
    label: 'gradient-accumulation-steps',
    value: 'gradient-accumulation-steps',
    desc: '梯度累积',
    type: 'inputNumber',
    min: 0,
    required: true,
  },
  {
    label: 'num-train-epochs',
    value: 'num-train-epochs',
    desc: '迭代轮次，控制模型训练过程中遍历整个数据集的次数。',
    type: 'inputNumber',
    min: 0,
    required: true,
  },
];
