<script setup lang="ts">
  import { reactive, ref, watch, nextTick, onBeforeMount, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import {
    EditOutlined,
    LoadingOutlined,
    PlusOutlined,
    DeleteOutlined,
    MinusOutlined,
    InfoCircleOutlined,
    FileTextOutlined,
  } from '@ant-design/icons-vue';
  import Accordion from '../../Knowledge/Components/Accordion.vue';
  import dayjs from 'dayjs';
  import { message } from 'ant-design-vue';
  import { uploadImages } from '@/api/exploration';
  import { DEFAULT_CALLPARAMS, example, templateStr } from '..';
  import AddKnowledge from './addKnowledge.vue';
  import type { IKnowledgeFilterItem } from '@/interface/knowledge';
  import ModelConfig from './modelConfig.vue';
  import { agentVersionDetail, checkAgentName, updateAgent } from '@/api/agent';
  import type { Followup_config, IAgentItem, IUpdateAgentProps } from '@/interface/agent';
  import type { ICallParams } from '@/interface/exploration';
  import { debounce } from '@/utils/common';
  import Preview from '../preview.vue';
  import type { Rule } from 'ant-design-vue/es/form';
  import bg1 from '@/assets/image/background/background_1.png';
  import bg2 from '@/assets/image/background/background_2.png';
  import bg3 from '@/assets/image/background/background_3.png';
  import bg4 from '@/assets/image/background/background_4.png';
  import bg5 from '@/assets/image/background/background_5.png';
  import bg6 from '@/assets/image/background/background_6.png';

  // 定义 props
  interface Props {
    agentId: string;
  }
  const props = defineProps<Props>();

  // 定义 emits
  const emit = defineEmits<{
    'auto-save-time-update': [time: string];
    'app-name-update': [name: string];
    'publish-request': [];
    'config-change': [hasChanges: boolean];
    'agent-detail-update': [detail: IAgentItem];
  }>();

  interface IDefaultCloseUpDStatus {
    basic: boolean;
    instruction: boolean;
    knowledge: boolean;
    dialogue: boolean;
    memory: boolean;
  }
  const defaultCloseUpDStatus: IDefaultCloseUpDStatus = {
    basic: true,
    instruction: true,
    knowledge: true,
    dialogue: true,
    memory: true,
  };
  const upStatus = reactive({
    ...defaultCloseUpDStatus,
  });
  const knowledgeUpStatue = reactive({
    knowledgebase: true,
  });
  const dialogueUpStatue = reactive({
    prologue: true,
    suggested: true,
    background: true,
  });
  const memoryUpStatue = reactive({
    variable: true,
  });
  const columns = [
    {
      title: '字段名称',
      dataIndex: 'name',
      key: 'name',
      max: 50,
      desc: ['记忆变量的字段名称，输入要求如下：', '1.仅支持英文、数字、下划线', '2.必须以英文字母开头'],
    },
    {
      title: '字段描述',
      dataIndex: 'description',
      key: 'description',
      max: 200,
      desc: [
        '详细填写记忆变量描述，大模型将根据描述来调用并读写记忆变量，完备的记忆变量描述能够提高调用的准确性。',
        '示例：用于记录患者的姓名',
      ],
    },
    {
      title: '默认值',
      dataIndex: 'default_value',
      key: 'default_value',
      max: 500,
      desc: ['预先设定字段的默认值'],
    },
    {
      title: '记忆时长',
      dataIndex: 'duration',
      key: 'duration',
      width: 150,
      desc: [
        '用户数据记忆时长，',
        '-如选择「永久」，则该应用用户一旦赋予变量值后将会永久存储，不会随着会话关闭而恢复为默认值。',
        '-如选择「单次会话」，当一次会话结束后，则应用用户赋予的变量值会自动恢复为默认值，仅对一次会话生效。',
      ],
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 100,
    },
  ];

  const route = useRoute();
  const iconFormRef = ref();
  const nameFormRef = ref();
  const addKnowledgeRef = ref();
  const agentName = ref('');
  const memoryRegex = /^[a-zA-Z][a-zA-Z0-9_]*$/;

  interface IDefault_state {
    icon_url: string;
    name: string;
    introduction: string;
    [key: string]: any;
  }
  const DEFAULT_FORM_STATE: IDefault_state = {
    icon_url: '',
    name: '',
    introduction: '',
  };
  const formState = reactive<IDefault_state>({ ...DEFAULT_FORM_STATE });
  const roleInstruction = reactive({
    value: '',
    is_enable: true,
  });
  const openerPrompt = reactive({
    value: '',
    is_enable: false,
  });
  const suggestedQuestions = reactive({
    value: [''],
    is_enable: true,
  });
  const followupConfig = reactive<Followup_config>({
    value: { svc_id: '', dialogue_rounds: 1 },
    is_enable: false,
  });
  const callParams = reactive<ICallParams>({ ...DEFAULT_CALLPARAMS });
  const generationConfig = reactive({
    value: callParams,
    is_enable: true,
  });
  const deployId = ref('');
  const masterId = ref('');

  const backgroundImage = reactive({
    value: '',
    is_enable: false,
  });
  interface IMemoryValue {
    name: string;
    description: string;
    default_value: string;
    duration: string;
  }
  const memoryConfig = reactive<{ value: IMemoryValue[] | []; is_enable: boolean }>({
    value: [],
    is_enable: true,
  });
  const customMemoryConfig = reactive<IMemoryValue[]>([]);
  const knowledgeDbList = ref<IKnowledgeFilterItem[]>([]);
  const knowledgeDb = reactive<{ value: string[]; is_enable: boolean }>({
    value: [],
    is_enable: false,
  });
  const state = reactive({
    updateLoading: false,
    knowledgeVisible: false,
    backgroundLoading: false,
    memoryVisible: false,
    backgroundImageVisible: false,
    backgroundSelectVisible: false,
  });
  const reloadKey = ref(0);

  // 保存初始状态用于比较
  const initialState = ref({});

  const closeUpStatus = (target: keyof typeof resouce, resouce: Record<string, boolean>) => {
    const temp = { ...resouce };
    if (!temp[target]) {
      Object.keys(temp).forEach((key) => {
        temp[key] = false;
      });
      temp[target] = true;
    } else {
      temp[target] = false;
    }
    Object.assign(resouce, temp);
  };

  const confirmSave = async (key?: string) => {
    await iconFormRef.value.validateFields();
    // 只有在修改名字时才需要校验重名
    if (key && key === 'name') {
      await nameFormRef.value.validateFields();
    }
    // 过滤空白推荐问
    const params: IUpdateAgentProps = {};
    if (key === 'deploy_id') {
      params.deploy_id = deployId.value;
    } else if (key) {
      // @ts-expect-error
      params[key] = formState[key];
    } else {
      const suggest = suggestedQuestions.value.filter((val) => val);
      params.config = {
        instruction: roleInstruction,
        knowledge_db: knowledgeDb,
        memory_config: memoryConfig,
        opener_prompt: openerPrompt,
        background_image: backgroundImage,
        suggested_questions: { value: suggest, is_enable: suggestedQuestions.is_enable },
        generation_config: generationConfig,
        followup_config: followupConfig,
      };
    }
    await updateAgent(props.agentId, params);
    const updated_at = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
    emit('auto-save-time-update', updated_at);
    reloadKey.value++;
  };

  const debouncedSave = debounce(confirmSave);

  const handleRemove = (record: IKnowledgeFilterItem) => {
    const index = knowledgeDbList.value.findIndex((item) => item === record);
    knowledgeDbList.value.splice(index, 1);
    knowledgeDb.value.splice(index, 1);
  };

  const uploadProps = {
    // @ts-expect-error
    beforeUpload: (file: UploadProps['fileList'][number]) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
      if (!isJpgOrPng) {
        message.warn('上传的图片格式不支持！');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('上传的图片太大！');
        return false;
      }
      return isJpgOrPng && isLt10M;
    },
    customRequest: async (detail: { file: File }) => {
      const file = detail.file;
      const formData = new FormData();
      formData.append('files', file);
      state.updateLoading = true;
      const res: { url: string }[] = await uploadImages(formData);
      formState.icon_url = res[0].url;
      debouncedSave('icon_url');
      state.updateLoading = false;
    },
    multiple: false,
    fileList: [],
    accept: 'image/png,image/jpg,image/jpeg',
    showUploadList: false,
  };

  // const uploadBackgroundProps = {
  //   // @ts-expect-error
  //   beforeUpload: (file: UploadProps['fileList'][number]) => {
  //     const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
  //     if (!isJpgOrPng) {
  //       message.warn('上传的图片格式不支持！');
  //       return false;
  //     }
  //     const isLt10M = file.size / 1024 / 1024 < 10;
  //     if (!isLt10M) {
  //       message.error('上传的图片太大！');
  //       return false;
  //     }
  //     return isJpgOrPng && isLt10M;
  //   },
  //   customRequest: async (detail: { file: File }) => {
  //     const file = detail.file;
  //     const formData = new FormData();
  //     formData.append('files', file);
  //     state.backgroundLoading = true;
  //     const res: { url: string }[] = await uploadImages(formData);
  //     backgroundImage.value = res[0].url;
  //     backgroundImage.is_enable = true;
  //     state.backgroundLoading = false;
  //   },
  //   multiple: false,
  //   fileList: [],
  //   accept: 'image/png,image/jpg,image/jpeg',
  //   showUploadList: false,
  // };

  const validatorName = async (_rule: Rule, value: string) => {
    if (value == '' || value.trim().length == 0) {
      return Promise.reject('请输入应用名称');
    }
    if (value.length > 20) {
      return Promise.reject('应用名称最多输入 20 个字');
    }
    if (value !== agentName.value) {
      try {
        await checkAgentName(value.trim());
      } catch (e) {
        if (e === 'AlreadyExists') {
          return Promise.reject('该名称已存在，请重新命名');
        }
        return Promise.reject(e);
      }
    }
    return Promise.resolve();
  };

  const rules = {
    name: [{ required: true, validator: validatorName }],
    logo: [{ required: true, message: '请选择应用图标' }],
  };

  const handleCancel = () => {
    knowledgeDbList.value = addKnowledgeRef.value.selected_knowledge_db;
    knowledgeDb.value = addKnowledgeRef.value.selected_knowledge_db.map((item: IKnowledgeFilterItem) => item.id);
    knowledgeDb.is_enable = Boolean(addKnowledgeRef.value.selected_knowledge_db.length);
  };

  const confirmAddMemory = () => {
    const errMsg = validMemory();
    if (errMsg) {
      message.warn(errMsg);
    } else {
      memoryConfig.value = JSON.parse(JSON.stringify(customMemoryConfig));
      state.memoryVisible = false;
    }
  };

  const openerPromptChange = (e: { target: { value: string } }) => {
    openerPrompt.value = e.target.value;
    openerPrompt.is_enable = Boolean(e.target.value);
  };

  const suggestedQuestionsChange = (index: number) => {
    const value = suggestedQuestions.value[index];
    if (suggestedQuestions.value.length < 3 && value.trim() && index === suggestedQuestions.value.length - 1) {
      suggestedQuestions.value.push('');
    }
    suggestedQuestions.is_enable = Boolean(suggestedQuestions.value.length);
  };

  const validMemory = () => {
    let errMsg = '';
    if (!customMemoryConfig || !customMemoryConfig.length) {
      return errMsg;
    } else {
      const len = customMemoryConfig.length - 1;
      const current = customMemoryConfig[len];
      const { name, description } = current;
      if (name == '' || name.trim().length == 0) {
        return (errMsg = '请输入字段名称');
      }
      if (!memoryRegex.test(name)) {
        return (errMsg = '格式不正确：字段名称仅支持英文字母、数字、-，且必须以英文字母开头');
      }
      if (
        customMemoryConfig
          .slice(0, -1)
          .map((item) => item.name)
          .includes(name)
      ) {
        return (errMsg = `${name}已存在，请重新命名`);
      }
      if (description == '' || description.trim().length == 0) {
        return (errMsg = '请输入字段描述');
      }
    }
  };

  const handleAddMemory = async () => {
    const errMsg = validMemory();
    if (errMsg) {
      message.warn(errMsg);
    } else {
      customMemoryConfig.push({
        //@ts-expect-error
        agent_id: masterId.value,
        name: '',
        description: '',
        duration: 'everlasting',
        default_value: '',
      });
    }
  };

  const handleDeleteMemory = (index: number) => {
    customMemoryConfig.splice(index, 1);
  };

  const handleOpenMemoryModel = () => {
    state.memoryVisible = true;
    if (customMemoryConfig.length === 0) {
      customMemoryConfig.push({
        //@ts-expect-error
        agent_id: masterId.value,
        name: '',
        description: '',
        duration: 'everlasting',
        default_value: '',
      });
    }
  };

  const fetchAgentDetail = async () => {
    const data: IAgentItem = await agentVersionDetail(props.agentId);
    const { name, introduction, icon_url, config, deploy_id, master_id } = data;
    agentName.value = name;
    deployId.value = deploy_id;
    masterId.value = master_id;
    const {
      instruction,
      knowledge_db,
      memory_config,
      opener_prompt,
      background_image,
      suggested_questions,
      generation_config,
      followup_config,
    } = config;
    Object.assign(formState, { name, introduction, icon_url });
    emit('agent-detail-update', data);
    Object.assign(roleInstruction, { ...instruction });
    Object.assign(knowledgeDb, { ...knowledge_db, value: knowledge_db.value.map((item) => item.id) });
    knowledgeDbList.value = knowledge_db.value;
    Object.assign(memoryConfig, { ...memory_config });
    const memory_config_value = memory_config.value;
    Object.assign(customMemoryConfig, memory_config_value);
    Object.assign(openerPrompt, { ...opener_prompt });
    Object.assign(backgroundImage, { ...background_image });
    Object.assign(generationConfig, { ...generation_config });
    Object.assign(followupConfig, { ...followup_config });
    const repetition_penalty_value = JSON.parse(JSON.stringify(generationConfig.value));
    generationConfig.value = repetition_penalty_value;
    if (repetition_penalty_value.repetition_penalty) {
      generationConfig.value.frequency_penalty = repetition_penalty_value.repetition_penalty;
    }
    // @ts-expect-error
    delete generationConfig.value.repetition_penalty;
    // 当推荐问题数量小于三个或没有时，自动补一个空白问题，方便用户输入
    const suggest =
      suggested_questions.value && suggested_questions.value.length
        ? suggested_questions.value.length < 3
          ? [...suggested_questions.value, '']
          : suggested_questions.value
        : [''];
    Object.assign(suggestedQuestions, {
      value: suggest,
      is_enable: suggested_questions.is_enable,
    });
  };

  const handleChangeGenerationConfig = async (deploy_id: string, value?: ICallParams) => {
    if (value) {
      Object.assign(generationConfig.value, { ...value });
    }
    deployId.value = deploy_id;
    // 当模型 id 存在时触发更新
    if (deploy_id) {
      await debouncedSave('deploy_id');
    }
  };

  const handleChangefollowupConfig = async (deploy_id: string) => {
    followupConfig.value.svc_id = deploy_id || null;
    await debouncedSave();
  };

  onBeforeMount(async () => {
    await fetchAgentDetail();
  });

  watch(
    [
      () => roleInstruction,
      () => knowledgeDb,
      () => openerPrompt,
      () => memoryConfig,
      () => generationConfig,
      () => suggestedQuestions,
      () => backgroundImage,
      () => followupConfig,
    ],
    async () => {
      await nextTick();
      await debouncedSave();
    },
    { deep: true },
  );

  // 添加预设背景图片数据
  const presetBackgrounds = ref([
    { id: 1, url: bg1, name: 'background_1' },
    { id: 2, url: bg2, name: 'background_2' },
    { id: 3, url: bg3, name: 'background_3' },
    { id: 4, url: bg4, name: 'background_4' },
    { id: 5, url: bg5, name: 'background_5' },
    { id: 6, url: bg6, name: 'background_6' },
    // 添加更多预设背景
  ]);

  // 处理背景图片选择
  const handleBackgroundSelect = (bgUrl: string) => {
    backgroundImage.value = bgUrl;
    backgroundImage.is_enable = true;
    state.backgroundSelectVisible = false;
    // message.success('背景图片已选择');
  };

  // 修改上传组件为点击选择
  const openBackgroundSelector = () => {
    state.backgroundSelectVisible = true;
  };

  // 在获取数据后保存初始状态
  const getAgentDetail = async () => {
    try {
      const res = await agentVersionDetail(props.agentId);
      // 设置表单数据...

      // 保存初始状态用于比较
      initialState.value = JSON.stringify({
        formState,
        roleInstruction,
        knowledgeDb,
        openerPrompt,
        memoryConfig,
        generationConfig,
        suggestedQuestions,
        backgroundImage,
        followupConfig,
      });

      // 初始状态下没有修改
      emit('config-change', false);
    } catch (error) {
      console.error(error);
    }
  };

  // 监听所有可能的修改
  watch(
    [
      () => formState,
      () => roleInstruction,
      () => knowledgeDb,
      () => openerPrompt,
      () => memoryConfig,
      () => generationConfig,
      () => suggestedQuestions,
      () => backgroundImage,
      () => followupConfig,
    ],
    () => {
      // 比较当前状态与初始状态
      const currentState = JSON.stringify({
        formState,
        roleInstruction,
        knowledgeDb,
        openerPrompt,
        memoryConfig,
        generationConfig,
        suggestedQuestions,
        backgroundImage,
        followupConfig,
      });

      // 如果状态不同，则有未发布的修改
      const hasChanges = currentState !== initialState.value;
      emit('config-change', hasChanges);
    },
    { deep: true },
  );

  onMounted(() => {
    getAgentDetail();
  });
</script>

<template>
  <a-row style="height: 100%">
    <a-col :span="12" style="height: 100%">
      <div class="left">
        <div class="left-config flex justify-between items-center">
          <div class="title-text text-[16px]">应用配置</div>
          <ModelConfig
            :deploy-id="deployId"
            :generation-config="generationConfig.value"
            @change="handleChangeGenerationConfig"
          />
        </div>
        <div class="left-content overflow-scroll">
          <Accordion :expend="upStatus.basic" :expand-click="() => closeUpStatus('basic', upStatus)" :bordered="false">
            <template #title>
              <div class="title-text">基本信息</div>
            </template>
            <template #content>
              <a-row>
                <a-col :span="6">
                  <a-form ref="iconFormRef" autocomplete="off" layout="vertical" :model="formState">
                    <a-form-item label="应用图标" name="icon_url" :rules="rules.logo">
                      <a-upload class="avatar-uploader" v-bind="uploadProps" list-type="picture-card">
                        <div v-if="formState.icon_url" class="img-container">
                          <div class="img-content" :style="{ backgroundImage: `url(${formState.icon_url})` }" />
                          <div class="overlay">
                            <div class="edit-icon">
                              <EditOutlined style="color: #ddd; font-size: 24px" />
                            </div>
                          </div>
                        </div>
                        <div v-else>
                          <loading-outlined v-if="state.updateLoading"></loading-outlined>
                          <plus-outlined v-else></plus-outlined>
                          <div class="ant-upload-text">上传图标</div>
                        </div>
                      </a-upload>
                    </a-form-item>
                  </a-form>
                </a-col>
                <a-col :span="18">
                  <a-form ref="nameFormRef" autocomplete="off" layout="vertical" :model="formState">
                    <a-form-item label="应用名称" name="name" :rules="rules.name">
                      <a-input
                        v-model:value="formState.name"
                        :maxlength="20"
                        placeholder="请输入应用名称"
                        show-count
                        @change="debouncedSave('name')"
                      ></a-input>
                    </a-form-item>
                  </a-form>
                </a-col>
              </a-row>
              <a-form-item label="应用简介" name="introduction">
                <a-textarea
                  ref="textareaRef"
                  v-model:value="formState.introduction"
                  allow-clear
                  show-count
                  :maxlength="500"
                  :auto-size="{ minRows: 6, maxRows: 6 }"
                  placeholder="请输入应用简介"
                  @change="debouncedSave('introduction')"
                />
              </a-form-item>
            </template>
          </Accordion>
          <Accordion :expend="upStatus.instruction" :expand-click="() => closeUpStatus('instruction', upStatus)">
            <template #title>
              <div class="flex justify-between items-center w-[100%]">
                <div class="title-text">
                  角色指令
                  <a-tooltip>
                    <template #title>
                      通过角色指令功能，你能够精确设定智能体应用的作用范围。包括指定应用将扮演的角色和输出结果的格式与风格。此外，你还可以规定应用不得执行哪些操作等。
                    </template>
                    <InfoCircleOutlined />
                  </a-tooltip>
                </div>
                <div @click.stop>
                  <a-popover title="示例" trigger="hover">
                    <template #content>
                      <div class="template-display overflow-scroll">
                        {{ example }}
                      </div>
                    </template>
                    <a>示例</a>
                  </a-popover>
                  <a-popover title="模版内容" trigger="hover">
                    <template #content>
                      <div class="template-display overflow-scroll">
                        {{ templateStr }}
                      </div>
                    </template>
                    <a class="ml-[10px]" @click.stop="roleInstruction.value = templateStr"
                      ><FileTextOutlined />使用模板</a
                    >
                  </a-popover>
                </div>
              </div>
            </template>
            <template #content>
              <a-textarea
                v-model:value="roleInstruction.value"
                allow-clear
                :auto-size="{ minRows: 8, maxRows: 8 }"
                placeholder="编写系统提示词，包括角色设定、任务目标、具备的能力及回复的要求与限制等，好的提示词会直接影响智能体效果。"
              />
            </template>
          </Accordion>

          <Accordion :expend="upStatus.knowledge" :expand-click="() => closeUpStatus('knowledge', upStatus)">
            <template #title>
              <div class="title-text">知识</div>
            </template>
            <template #content>
              <Accordion
                :expend="knowledgeUpStatue.knowledgebase"
                :expand-click="() => closeUpStatus('knowledgebase', knowledgeUpStatue)"
              >
                <template #title>
                  <div class="flex justify-between items-center w-[100%]">
                    <div class="title-text">知识库</div>
                    <div @click.stop>
                      <a-switch
                        v-model:checked="knowledgeDb.is_enable"
                        @change="message.success(knowledgeDb.is_enable ? '已开启知识库' : '已关闭知识库')"
                      />

                      <a-tooltip>
                        <template #title> 添加知识库 </template>
                        <PlusOutlined
                          style="margin-left: 10px; color: #1890ff"
                          @click.stop="state.knowledgeVisible = true"
                        />
                      </a-tooltip>
                    </div>
                  </div>
                </template>
                <template #content>
                  <div v-if="knowledgeDbList && knowledgeDbList.length" class="knowledge-box overflow-scroll">
                    <div v-for="item in knowledgeDbList" :key="item.id" class="flex justify-between knowledge-item">
                      <div class="flex items-center">
                        <div>{{ item.name }}</div>
                        <a-popover>
                          <template #content>
                            <p class="title-text">
                              {{
                                item.files.filter((item) => item.status === 'available').length
                                  ? `${item.name} 可用文件`
                                  : '暂无可用文件'
                              }}
                            </p>
                            <div
                              v-for="file in item.files.filter((item) => item.status === 'available')"
                              :key="file.id"
                              class="mb-[5px]"
                            >
                              {{ file.name }}
                            </div>
                          </template>
                          <a-tag color="green" style="margin: 0 5px; width: 40px"
                            >{{ item.files.filter((item) => item.status === 'available').length }}个</a-tag
                          >
                        </a-popover>
                      </div>
                      <div class="flex items-center">
                        <div class="flex items-center">
                          <template v-if="item.files.some((file) => file.status === 'available')">
                            <svg class="knowledge-icon" aria-hidden="true">
                              <use xlink:href="#icon-chenggong"></use>
                            </svg>
                          </template>
                          <template v-else>
                            <span class="text-[#d9001b]">此知识库无可用文件</span>
                            <svg class="knowledge-icon" aria-hidden="true">
                              <use xlink:href="#icon-warning"></use>
                            </svg>
                          </template>
                        </div>
                        <a-button danger shape="circle" size="small" @click="handleRemove(item)"
                          ><MinusOutlined
                        /></a-button>
                      </div>
                    </div>
                  </div>
                  <div v-else class="text-[#797979] text-[14px]">
                    可上传本地文本数据、表格型知识数据构建知识库。
                    用户发送消息时，应用能够引用知识库答复。应用最多可关联 50
                    个知识库。建议详细填写知识库描述信息以提高问答准确率
                  </div>
                </template>
              </Accordion>
            </template>
          </Accordion>

          <Accordion :expend="upStatus.dialogue" :expand-click="() => closeUpStatus('dialogue', upStatus)">
            <template #title>
              <div class="title-text">对话</div>
            </template>
            <template #content>
              <Accordion
                :expend="dialogueUpStatue.prologue"
                :expand-click="() => closeUpStatus('prologue', dialogueUpStatue)"
              >
                <template #title>
                  <div class="flex justify-between items-center w-[100%]">
                    <div class="title-text">开场白</div>
                    <div @click.stop>
                      <a-switch
                        v-model:checked="openerPrompt.is_enable"
                        @change="message.success(openerPrompt.is_enable ? '已开启开场白' : '已关闭开场白')"
                      />
                    </div>
                  </div>
                </template>
                <template #content>
                  <div class="pb-[20px]">
                    <a-textarea
                      v-model:value="openerPrompt.value"
                      allow-clear
                      show-count
                      :maxlength="500"
                      :auto-size="{ minRows: 8, maxRows: 8 }"
                      placeholder="请输入开场白"
                      @change="openerPromptChange"
                    />
                  </div>
                </template>
              </Accordion>
              <Accordion
                :expend="dialogueUpStatue.suggested"
                :expand-click="() => closeUpStatus('suggested', dialogueUpStatue)"
              >
                <template #title>
                  <div class="flex justify-between items-center w-[100%]">
                    <div class="title-text">推荐问</div>
                    <div @click.stop>
                      <a-switch
                        v-model:checked="suggestedQuestions.is_enable"
                        @change="message.success(suggestedQuestions.is_enable ? '已开启推荐问' : '已关闭推荐问')"
                      />
                    </div>
                  </div>
                </template>
                <template #content>
                  <div v-for="(_, index) in suggestedQuestions.value" :key="index" class="flex items-center mb-[10px]">
                    <a-input
                      v-model:value="suggestedQuestions.value[index]"
                      allow-clear
                      show-count
                      :maxlength="50"
                      placeholder="请输入"
                      @blur="suggestedQuestionsChange(index)"
                      @press-enter="suggestedQuestionsChange(index)"
                    />
                    <DeleteOutlined
                      v-if="suggestedQuestions.value.length > 1"
                      class="ml-[10px] text-red"
                      @click="suggestedQuestions.value.splice(index, 1)"
                    />
                  </div>
                </template>
              </Accordion>
              <Accordion
                :expend="dialogueUpStatue.suggested"
                :expand-click="() => closeUpStatus('suggested', dialogueUpStatue)"
              >
                <template #title>
                  <div class="flex justify-between items-center w-[100%]">
                    <div class="title-text">
                      追问
                      <a-tooltip>
                        <template #title> 在智能体回复后，自动根据对话内容生成追加问题，提供用户提问建议 </template>
                        <InfoCircleOutlined />
                      </a-tooltip>
                    </div>
                    <div @click.stop>
                      <a-switch
                        v-model:checked="followupConfig.is_enable"
                        @change="message.success(followupConfig.is_enable ? '已开启追问' : '已关闭追问')"
                      />
                    </div>
                  </div>
                </template>
                <template #content>
                  <div class="flex items-center">
                    <div class="w-[120px] flex flex-end">选用模型：</div>
                    <ModelConfig :deploy-id="followupConfig.value.svc_id" @change="handleChangefollowupConfig" />
                  </div>
                  <div class="flex items-center mt-[10px]">
                    <div class="w-[120px] flex">
                      参考对话轮数：
                      <a-tooltip>
                        <template #title> 大模型在追问生成过程中可参考 1、3、5 轮的对话轮数 </template>
                        <InfoCircleOutlined />
                      </a-tooltip>
                    </div>

                    <a-select
                      v-model:value="followupConfig.value.dialogue_rounds"
                      placeholder="请选择"
                      @change="followupConfig.is_enable = true"
                    >
                      <a-select-option :value="1">1</a-select-option>
                      <a-select-option :value="3">3</a-select-option>
                      <a-select-option :value="5">5</a-select-option>
                    </a-select>
                  </div>
                </template>
              </Accordion>
              <Accordion
                :expend="dialogueUpStatue.background"
                :expand-click="() => closeUpStatus('background', dialogueUpStatue)"
              >
                <template #title>
                  <div class="w-[100%]">
                    <div class="flex justify-between items-center w-[100%]">
                      <div class="title-text">
                        背景图片
                        <!-- <a-tooltip>
                        <template #title> 支持 PNG、JPG、JPEG 格式，文件大小 10M 以内 </template>
                        <InfoCircleOutlined />
                      </a-tooltip> -->
                    </div>
                    <div @click.stop>
                      <a-switch
                        v-model:checked="backgroundImage.is_enable"
                        @change="
                          (checked: boolean) => {
                            if (checked) {
                              if (presetBackgrounds.length > 0) {
                                backgroundImage.value = presetBackgrounds[0].url;
                              }
                              message.success('已开启背景图片');
                            } else {
                              backgroundImage.value = '';
                              message.success('已关闭背景图片');
                            }
                          }
                        "
                      />
                    </div>
                  </div>
                  <div class="text-[#8c8c8c] text-[12px] leading-[18px] mt-[4px]">打开选择背景图片，提供更沉浸的对话体验</div>
                </div>
              </template>
                <!-- 隐藏本地上传的功能 -->
                <!-- <template #content>
                  <a-upload class="avatar-uploader" v-bind="uploadBackgroundProps" list-type="picture-card">
                    <div v-if="backgroundImage.value" class="img-container">
                      <div class="img-content" :style="{ backgroundImage: `url(${backgroundImage.value})` }" />
                      <div class="overlay">
                        <EditOutlined style="color: #ddd; font-size: 24px" />
                        <DeleteOutlined
                          style="color: #ddd; font-size: 24px"
                          @click.stop="state.backgroundImageVisible = true"
                        />
                      </div>
                    </div>
                    <div v-else>
                      <loading-outlined v-if="state.backgroundLoading"></loading-outlined>
                      <plus-outlined v-else />
                      <div class="ant-upload-text">上传图片</div>
                    </div>
                  </a-upload>
                </template> -->
                <template #content>
                  <div v-if="backgroundImage.is_enable" class="background-selector" @click="openBackgroundSelector">
                    <div v-if="backgroundImage.value" class="img-container">
                      <div class="img-content" :style="{ backgroundImage: `url(${backgroundImage.value})` }" />
                      <div class="overlay">
                        <EditOutlined style="color: #ddd; font-size: 24px" />
                        <!-- <DeleteOutlined
                          style="color: #ddd; font-size: 24px"
                          @click.stop="state.backgroundImageVisible = true"
                        /> -->
                      </div>
                    </div>
                    <div v-else class="wh-full flex flex-col items-center justify-center">
                      <plus-outlined class="mb-[8px]" />
                      <div class="ant-upload-text">选择背景</div>
                    </div>
                  </div>
                </template>
              </Accordion>
            </template>
          </Accordion>
          <Accordion :expend="upStatus.memory" :expand-click="() => closeUpStatus('memory', upStatus)">
            <template #title>
              <div class="title-text">记忆</div>
            </template>
            <template #content>
              <Accordion
                :expend="memoryUpStatue.variable"
                :expand-click="() => closeUpStatus('variable', memoryUpStatue)"
              >
                <template #title>
                  <div class="flex justify-between items-center w-[100%]">
                    <div class="title-text">
                      记忆变量
                      <a-tooltip>
                        <template #title>
                          可配置的变量字段，系统依据字段描述自动抽取变量信息，保存的变量信息用于后续的决策。
                        </template>
                        <InfoCircleOutlined />
                      </a-tooltip>
                    </div>
                    <div>
                      <a-tooltip>
                        <template #title> 添加记忆变量 </template>
                        <PlusOutlined style="margin-left: 10px; color: #4096ff" @click.stop="handleOpenMemoryModel" />
                      </a-tooltip>
                    </div>
                  </div>
                </template>
                <template #content>
                  <div v-if="memoryConfig.value && memoryConfig.value.length" class="memory-box">
                    <div
                      v-for="(item, index) in memoryConfig.value"
                      :key="index"
                      class="memory-item"
                      @click="state.memoryVisible = true"
                    >
                      <a-popover>
                        <template #content>
                          <p>字段名称：{{ item.name }}</p>
                          <p>字段描述：{{ item.description }}</p>
                          <p>默认值：{{ item.default_value || '--' }}</p>
                          <p>记忆时长：{{ item.duration === 'everlasting' ? '永久' : '单次会话' }}</p>
                        </template>
                        {{ item.name }}
                      </a-popover>
                    </div>
                  </div>
                  <div v-else class="text-[#797979] text-[14px]">
                    可配置的变量字段，系统依据字段描述自动抽取变量信息，保存的变量信息用于后续的决策。
                  </div>
                </template>
              </Accordion>
            </template>
          </Accordion>
        </div>
      </div>
    </a-col>
    <a-col :span="12" style="height: 100%">
      <div class="right">
        <div class="title-text h-[62px] leading-[62px] pl-[20px] bg-[#f5f5f5] text-[16px]">预览与调试</div>
        <div class="preview-box bg-[#f5f5f5]">
          <Preview :key="reloadKey" :isNesting="false" />
        </div>
      </div>
    </a-col>
  </a-row>

  <a-modal
    v-model:open="state.backgroundImageVisible"
    width="400px"
    centered
    :mask-closable="false"
    title="确定删除背景图？"
    @ok="
      backgroundImage.value = '';
      backgroundImage.is_enable = false;
      state.backgroundImageVisible = false;
    "
    @cancel="state.backgroundImageVisible = false"
  >
    <div class="text-[#797979]">删除后，需重新发布应用才能生效</div>
  </a-modal>
  <a-modal
    v-model:open="state.knowledgeVisible"
    width="50%"
    centered
    :mask-closable="false"
    title="添加知识库"
    :footer="false"
    @cancel="handleCancel"
  >
    <AddKnowledge ref="addKnowledgeRef" :select="knowledgeDbList" />
  </a-modal>
  <a-modal v-model:open="state.memoryVisible" width="50%" centered title="添加记忆变量" @ok="confirmAddMemory">
    <p class="text-[#797979] text-[14px]">
      删除变量或修改变量名称/描述，应用更新发布后会导致应用用户对应的变量数据被删除或重置为默认值，请谨慎操作
    </p>
    <div>
      <a-table :columns="columns" :data-source="customMemoryConfig" bordered :pagination="false" :scroll="{ y: 450 }">
        <template #headerCell="{ column }">
          <template v-if="column.desc">
            <span v-if="['name', 'description'].includes(column.dataIndex)" class="text-red">*</span>
            {{ column.title }}
            <a-popover title="" trigger="hover">
              <template #content>
                <div v-for="item in column.desc" :key="item">
                  <p class="text-[#797979]">{{ item }}</p>
                </div>
              </template>
              <InfoCircleOutlined />
            </a-popover>
          </template>
        </template>
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'operation'">
            <DeleteOutlined color="red" @click="handleDeleteMemory(index)" />
          </template>
          <template v-else-if="column.dataIndex === 'duration'">
            <a-select v-model:value="record[column.dataIndex]" placeholder="请选择" style="width: 100%">
              <a-select-option value="everlasting">永久</a-select-option>
              <a-select-option value="once">单次会话</a-select-option>
            </a-select>
          </template>
          <template v-else>
            <a-input v-model:value="record[column.dataIndex]" show-count :maxlength="column.max" placeholder="请输入" />
          </template>
        </template>
      </a-table>
      <a-button style="margin-top: 16px" @click="handleAddMemory"><PlusOutlined />添加 </a-button>
    </div>
  </a-modal>
  <a-modal
    v-model:open="state.backgroundSelectVisible"
    title="选择背景图片"
    width="600px"
    :footer="null"
    @cancel="state.backgroundSelectVisible = false"
  >
    <div class="background-grid">
      <div
        v-for="bg in presetBackgrounds"
        :key="bg.id"
        class="background-item"
        :class="{ selected: backgroundImage.value === bg.url }"
        @click="handleBackgroundSelect(bg.url)"
      >
        <img :src="bg.url" :alt="bg.name" />
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
  .left {
    height: 100%;
    padding: 0 10px;
    display: flex;
    flex-direction: column;
    .left-config {
      border-bottom: 1px solid #ddd;
      height: 62px;
      padding: 10px 0;
    }
    .left-content {
      height: calc(100% - 42px);
      padding: 0 5px;
      flex: 1;
    }
  }
  .right {
    height: 100%;
    padding: 0 0 10px 10px;
    .preview-box {
      height: calc(100% - 52px);
    }
  }
  .img-container {
    width: 100%; /* 宽度自适应 */
    height: 100%; /* 高度自适应 */
    position: relative;
    &:hover {
      .overlay {
        opacity: 1;
      }
    }
  }
  .img-content {
    width: 100%; /* 宽度自适应 */
    height: 100%; /* 高度自适应 */
    // background-image: url('your-image.jpg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    justify-content: space-around;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  .template-display {
    white-space: pre-wrap;
    font-family: monospace;
    background-color: #f9f9f9;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #eee;
    margin: 10px 0;
    line-height: 1.6;
    color: #222;
    height: 300px;
  }
  .knowledge-box {
    max-height: 200px;
  }
  .knowledge-item {
    border: 1px solid #ccc;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 10px;
    .knowledge-icon {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      margin: 0 5px;
    }
  }
  .memory-box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
  }
  .memory-item {
    background: #f2f2f2;
    border-radius: 10px;
    max-width: 100px;
    padding: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    margin: 0 5px;
    margin-bottom: 5px;
  }
  .title-text {
    font-weight: bold;
  }
  .background-selector {
    width: 104px;
    height: 104px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: #4096ff;
    }
  }

  .background-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    max-height: 400px;
    overflow-y: auto;
  }

  .background-item {
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 8px;
    overflow: hidden;

    &:hover {
      border-color: #1677ff;
    }

    &.selected {
      border-color: #1677ff;
      box-shadow: 0 0 0 1px #1677ff;
    }

    img {
      width: 100%;
      height: 120px;
      object-fit: cover;
    }

    .bg-name {
      padding: 8px;
      text-align: center;
      background: #f5f5f5;
      font-size: 12px;
    }
  }
</style>
