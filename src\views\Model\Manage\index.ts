export const templates: { label: string, value: string }[] = [
  { label: 'deepseek', value: 'deepseek' },
  { label: 'deepseek3', value: 'deepseek3' },
  { label: 'deepseekr1', value: 'deepseekr1' },
  { label: 'llama2', value: 'llama2' },
  { label: 'llama3', value: 'llama3' },
  { label: 'llama4', value: 'llama4' },
  { label: 'qwen', value: 'qwen' },
  { label: 'qwen3', value: 'qwen3' },
]

export const modelCategory: { label: string, value: string }[] = [
  { label: 'llm', value: 'llm' },
  { label: 'ocr', value: 'ocr' },
  { label: 'avatar-2d', value: 'avatar-2d' },
  { label: 'multimodal', value: 'multimodal' },
  { label: 'sklearn-svc', value: 'sklearn-svc' },
  { label: 'sklearn-linearRegression', value: 'sklearn-linearRegression' },
  { label: 'sklearn-KMeans', value: 'sklearn-KMeans' },
  { label: 'p2l', value: 'p2l' },
  { label: 'blender', value: 'blender' },
]

export const machineLearningList: string[] = ['sklearn-svc', 'sklearn-linearRegression', 'sklearn-KMeans']

export const regressCategory: string[] = ['sklearn-linearRegression']
export const clusterCategory: string[] = ['sklearn-KMeans']
export const classifyCategory: string[] = ['sklearn-svc']