
<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { LeftOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { agentVersionDetail, publishAgent, publishChannel } from '@/api/agent';
  import type { IAgentItem } from '@/interface/agent';
  import ConfigTab from './Components/ConfigTab.vue';
  import PublishTab from './Components/PublishTab.vue';
  import { convertIsoTimeToLocalTime } from '@/utils/common';
  import Icon from '@/components/Icon/index.vue';

  const router = useRouter();
  const route = useRoute();
  const updated_at = ref('');
  // 根据 URL 参数设置默认标签页，如果有 tab=publish 参数则默认显示发布页面
  const currentTab = ref(route.query.tab === 'publish' ? 'publish' : 'config');
  const hasUnsavedChanges = ref(false);
  const agentDetail = ref<IAgentItem>();

  // 获取从列表页传递过来的发布时间
  const publishTime = ref(route.query.publishTime ? String(route.query.publishTime) : '');

  // 计算是否显示"有待发布的修改"提示
  const showPendingChanges = computed(() => !isNewStatus.value && hasUnsavedChanges.value);

  // 计算是否为新建状态（status为'new'或没有status字段）
  const isNewStatus = computed(() => {
    return route.query.status === 'new' || !route.query.status;
  });

  // 监听 currentTab 变化，更新 URL 参数
  watch(currentTab, (newTab) => {
    const query = { ...route.query };
    if (newTab === 'publish') {
      query.tab = 'publish';
    } else {
      delete query.tab;
    }
    router.replace({ query });
  });

  // 监听路由参数的 publishTime，保持"最近发布于"时间与发布后保持同步
  watch(
    () => route.query.publishTime,
    (newVal) => {
      publishTime.value = newVal ? decodeURIComponent(String(newVal)) : '';
    }
  );

  // 监听路由参数中的status变化，同步更新页面状态
  watch(
    () => route.query.status,
    (newStatus) => {
      // 当status从'new'变为其他状态时，需要重新获取详情以确保数据同步
      if (newStatus && newStatus !== 'new') {
        getAgentDetail();
      }
    }
  );

  const handleBack = () => {
    // 如果没有未保存的修改，清除localStorage状态
    if (!hasUnsavedChanges.value) {
      const savedChangesKey = `agent_unsaved_changes_${route.params.id}`;
      localStorage.removeItem(savedChangesKey);
    }
    router.back();
  };

  const handleAutoSaveTimeUpdate = (time: string) => {
    updated_at.value = time;
  };

  // 处理配置修改状态更新
  const handleConfigChange = (hasChanges: boolean) => {
    hasUnsavedChanges.value = hasChanges;

    // 同步更新localStorage中的修改状态，以便主页面能够正确显示红点
    const savedChangesKey = `agent_unsaved_changes_${route.params.id}`;
    if (hasChanges) {
      localStorage.setItem(savedChangesKey, 'true');
    } else {
      localStorage.removeItem(savedChangesKey);
    }
  };

  const confirmPublish = async () => {
    try {
      // 确保已获取到智能体详情
      if (!agentDetail.value) {
        const data: IAgentItem = await agentVersionDetail(String(route.params.id));
        agentDetail.value = data;
      }

      // 首次发布时，默认开通网页版
      const publishChannels = agentDetail.value?.publish_channel || [];
      const origins: any = (agentDetail.value as any)?.origins;
      if (!publishChannels.includes('website')) {
        await publishChannel(String(route.params.id), {
          publish_channel: ['website'],
          origins,
        } as any);
      }

      // 提交发布
      await publishAgent(String(route.params.id));

      // 重新获取最新详情（获取状态与发布时间）
      const latest: IAgentItem = await agentVersionDetail(String(route.params.id));
      agentDetail.value = latest;

      // 同步更新本地“最近发布于”时间
      publishTime.value = latest.updated_at || '';

      // 发布成功后跳转到当前智能体的"发布"页签
      message.success('已提交发布');
      hasUnsavedChanges.value = false; // 发布后重置修改状态

      // 清除localStorage中的修改状态
      const savedChangesKey = `agent_unsaved_changes_${route.params.id}`;
      localStorage.removeItem(savedChangesKey);

      currentTab.value = 'publish';

      // 使用 nextTick 确保DOM更新完成后再更新路由
      await nextTick();

      router.replace({
        path: `/intelligent/${route.params.id}`,
        query: {
          ...route.query,
          status: latest.status,
          publishTime: latest.updated_at || '',
          tab: 'publish',
          // 发布后清除未保存修改状态
          hasUnsavedChanges: undefined,
        },
      });

      // 确保状态完全同步
      refreshRouteStatus();
    } catch (error) {
      // message.error('发布失败');
      console.error(error);
    }
  };

  // 获取智能体详情
  const getAgentDetail = async () => {
    const data: IAgentItem = await agentVersionDetail(String(route.params.id));
    agentDetail.value = data;
    // 同步"最近发布于"时间为后端返回的更新时间
    publishTime.value = data.updated_at || '';
  };

  // 强制刷新路由状态，确保状态同步
  const refreshRouteStatus = () => {
    if (agentDetail.value?.status) {
      const currentQuery = { ...route.query };
      if (currentQuery.status !== agentDetail.value.status) {
        router.replace({
          query: {
            ...currentQuery,
            status: agentDetail.value.status,
          },
        });
      }
    }
  };

  onMounted(() => {
    getAgentDetail();
    // 页面初始化时清除修改状态，因为此时还没有任何修改
    const savedChangesKey = `agent_unsaved_changes_${route.params.id}`;
    localStorage.removeItem(savedChangesKey);
  });

  // 页面卸载时，如果没有未保存的修改，清除localStorage状态
  onUnmounted(() => {
    if (!hasUnsavedChanges.value) {
      const savedChangesKey = `agent_unsaved_changes_${route.params.id}`;
      localStorage.removeItem(savedChangesKey);
    }
  });

</script>

<template>
  <div class="header text-[18px]">
    <div>
      <LeftOutlined @click="handleBack" />
      <span class="m-l-[10px]">{{ agentDetail?.name }}</span>
    </div>
    <a-radio-group v-model:value="currentTab" class="mr-[10px] radio-group-center">
      <a-radio-button value="config">配置</a-radio-button>
      <a-popover v-if="isNewStatus" placement="top">
        <template #content>
          <p>请先完成发布</p>
        </template>
        <a-radio-button value="publish" :disabled="true">发布</a-radio-button>
      </a-popover>
      <a-radio-button v-else value="publish">发布</a-radio-button>
    </a-radio-group>
    <div v-if="currentTab === 'config'" class="flex text-[14px] text-[#797979]">
      <div v-if="showPendingChanges" class="leading-[32px] mr-[40px]">有待发布的修改</div>
      <div class="leading-[32px] mr-[10px]">自动保存于：{{ updated_at }}</div>

      <!-- 只有非新建状态才显示 Popover -->
      <a-popover v-if="!isNewStatus" placement="bottomRight">
        <template #content>
          <p>最近发布于{{ publishTime ? convertIsoTimeToLocalTime(publishTime) : '--' }}</p>
          <p class="flex flex-start items-center text-center">
            <Icon class="mr-[5px] mt-[2px]" name="warning" size="16" />
            <span>将同时更新至已完成配置的 {{ agentDetail?.publish_channel?.length || 0 }} 个渠道，应用效果可能会变化</span>
          </p>
          <p class="flex mt-[10px]">
            <span v-for="channel in agentDetail?.publish_channel || []" :key="channel">
              <span class="bg-[#e8eaee] px-[10px] py-[5px] mr-[10px] rounded-2 flex items-center">
                <Icon :name="channel === 'website' ? 'dakaiwangyeban' : 'Modals'" size="16" class="mr-[5px]" />
                <span> {{ channel === 'website' ? '网页版' : '网站嵌入' }} </span>
              </span>
            </span>
          </p>
        </template>
        <a-button :disabled="!showPendingChanges" type="primary" @click="confirmPublish"> 更新发布 </a-button>
      </a-popover>

      <!-- 新建状态只显示简单的发布按钮 -->
      <a-button v-else type="primary" @click="confirmPublish"> 发布 </a-button>
    </div>
  </div>
  <div class="container">
    <keep-alive>
      <ConfigTab
        v-if="currentTab === 'config'"
        :agent-id="String(route.params.id)"
        @auto-save-time-update="handleAutoSaveTimeUpdate"
        @publish-request="confirmPublish"
        @config-change="handleConfigChange"
      />
      <PublishTab
        v-else-if="currentTab === 'publish'"
        :agent-id="String(route.params.id)"
        :agent-detail="agentDetail"
        @agent-detail-update="getAgentDetail"
      />
    </keep-alive>
  </div>
</template>

<style scoped lang="less">
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #ccc;
    position: relative;
    height: 42px;
  }
  .container {
    height: calc(100% - 40px);
  }
  .radio-group-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  :deep(.ant-tooltip-inner) {
    width: 250px;
    padding: 8px;
    background: rgb(23 24 26 / 75%);
    border-radius: 4px;

    .tip-item {
      font-size: 10px;
      font-weight: 400;
      line-height: 14px;
      color: #fff;
    }
  }
</style>



