import type { ICreateDistillationTask, IFetchDistillationList } from '@/interface/distillation';
import { getEnv } from '@/utils/getEnv';
import request from '@/utils/request';

const { VITE_APP_V2_BASE_URL } = getEnv();

const V2 = VITE_APP_V2_BASE_URL;


export function checkDistillationName(name: string) {
  return request.$Axios.post('/distillation/name/check', { name });
}
export function checkDistillationOutputName(name: string) {
  return request.$Axios.post('/distillation/output_name/check', { name });
}
export function featchDistillationList(data: IFetchDistillationList) {
  return request.$Axios.post(`/distillation/task/list`, data);
}
export function featchDistillationFillterList(distillation_role: 'teacher' | 'student') {
  return request.$Axios.get(`/model/distillation/filter_list[${V2}]`, { distillation_role });
}
export function createDistillationTask(data: ICreateDistillationTask) {
  return request.$Axios.post(`/distillation/create/task`, data);
}
export function fetchDistillationDetail(task_id: string) {
  return request.$Axios.get(`/distillation/task/detail/${task_id}`);
}
export function getDistillationTaskLogs(task_id: string) {
  return request.$Axios.get(`/distillation/task/logs/${task_id}`);
}
export function stopDistillationTask(task_id: string) {
  return request.$Axios.post(`/distillation/task/stop/${task_id}`);
}
export function deleteDistillationTask(task_id: string) {
  return request.$Axios.post(`/distillation/task/delete/${task_id}`);
}
export function getDistillationMonitor(data: { id: string, start_time: string, end_time: string, time_unit: number }) {
  const { id, start_time, end_time, time_unit } = data
  return request.$Axios.get(`/distillation/task/system_resource/${id}`, { start_time, end_time, time_unit });
}